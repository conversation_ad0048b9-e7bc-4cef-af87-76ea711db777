"""
简单的 MySQL 连接测试
不依赖其他服务，独立测试 MySQL 连接
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_mysql_connection():
    """直接测试 MySQL 连接"""
    print("🚀 简单 MySQL 连接测试")
    print("=" * 50)
    
    try:
        import pymysql
        print("✅ pymysql 模块导入成功")
    except ImportError:
        print("❌ pymysql 模块未安装")
        print("💡 请运行: pip install pymysql")
        return False
    
    # 连接配置
    config = {
        'host': 'localhost',
        'port': 3306,
        'user': 'root',
        'password': '5Secsgo100',
        'database': 'chestnut_cms',
        'charset': 'utf8mb4',
        'connect_timeout': 10
    }
    
    print(f"🔗 尝试连接到: {config['host']}:{config['port']}/{config['database']}")
    
    try:
        # 建立连接
        connection = pymysql.connect(**config)
        print("✅ 数据库连接成功！")
        
        with connection.cursor() as cursor:
            # 测试基本查询
            cursor.execute("SELECT VERSION() as version")
            version = cursor.fetchone()
            print(f"📊 MySQL 版本: {version[0]}")
            
            # 测试数据库
            cursor.execute("SELECT DATABASE() as db")
            db = cursor.fetchone()
            print(f"📁 当前数据库: {db[0]}")
            
            # 获取表列表
            cursor.execute("SHOW TABLES")
            tables = cursor.fetchall()
            print(f"📄 表数量: {len(tables)}")
            
            if tables:
                print("📋 表列表:")
                for i, table in enumerate(tables, 1):
                    print(f"  {i}. {table[0]}")
            else:
                print("📋 数据库中没有表")
        
        connection.close()
        print("\n✅ 测试完成，连接正常！")
        return True
        
    except pymysql.Error as e:
        print(f"\n❌ 数据库连接失败!")
        print(f"错误代码: {e.args[0] if e.args else '未知'}")
        print(f"错误信息: {e}")
        
        print(f"\n💡 常见解决方案:")
        print(f"  1. 检查 MySQL 服务是否启动")
        print(f"  2. 确认数据库 'chestnut_cms' 是否存在")
        print(f"  3. 验证用户名密码: root/5Secsgo100")
        print(f"  4. 检查 MySQL 是否允许本地连接")
        
        return False
    
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        return False

if __name__ == "__main__":
    test_mysql_connection()