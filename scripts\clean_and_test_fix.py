#!/usr/bin/env python3
"""
清理数据库并测试修复后的文档处理
"""
import os
import sys
import shutil
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

from backend.config import settings
from backend.app.services.rag_service import RAGService
from backend.app.services.chestnut_cms_service import ChestnutCMSService

def clean_database():
    """清理ChromaDB数据库"""
    print("🧹 清理ChromaDB数据库...")
    
    storage_path = settings.chroma_persist_directory
    if storage_path.exists():
        try:
            shutil.rmtree(storage_path)
            print(f"✅ 已删除存储目录: {storage_path}")
        except Exception as e:
            print(f"❌ 删除存储目录失败: {e}")
            return False
    
    # 重新创建目录
    storage_path.mkdir(exist_ok=True)
    print(f"✅ 重新创建存储目录: {storage_path}")
    return True

def test_document_processing():
    """测试文档处理功能"""
    print("\n📄 测试文档处理功能...")
    
    try:
        # 初始化RAG服务
        rag_service = RAGService()
        print("✅ RAG服务初始化成功")
        
        # 创建测试文档
        test_content = """测试文档标题

这是一个测试文档的内容。
包含多行文本用于测试文档处理功能。
测试向量化和检索是否正常工作。

关键词：测试、文档、向量化、检索
"""
        
        test_filename = "test_document.txt"
        test_metadata = {
            "title": "测试文档标题",
            "content_id": "test_001",
            "source": "test",
            "file_url": "https://example.com/test"
        }
        
        # 上传测试文档
        print(f"📤 上传测试文档: {test_filename}")
        result = rag_service.upload_document(test_content, test_filename, test_metadata)
        
        if result["success"]:
            print("✅ 文档上传成功!")
            print(f"  - 文件名: {result['filename']}")
            print(f"  - 新块数: {result['new_chunks']}")
            print(f"  - 总块数: {result['total_chunks']}")
        else:
            print(f"❌ 文档上传失败: {result['message']}")
            return False
        
        # 测试查询功能
        print("\n🔍 测试查询功能...")
        query_result = rag_service.query("测试文档的内容是什么？")
        
        if query_result["success"]:
            print("✅ 查询成功!")
            print(f"  - 回答: {query_result['answer'][:100]}...")
            print(f"  - 找到源文档数: {query_result['total_sources']}")
            
            # 检查源文档元数据
            if query_result['sources']:
                source = query_result['sources'][0]
                metadata = source.get('metadata', {})
                print(f"  - 源文档标题: {metadata.get('title')}")
                print(f"  - 源文档URL: {metadata.get('file_url')}")
                print(f"  - 源文档来源: {metadata.get('source')}")
        else:
            print(f"❌ 查询失败: {query_result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ 测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_chestnut_cms_integration():
    """测试ChestnutCMS集成"""
    print("\n🌰 测试ChestnutCMS集成...")
    
    try:
        cms_service = ChestnutCMSService()
        
        # 获取一篇测试文章
        articles = cms_service.get_latest_articles(limit=1)
        if not articles:
            print("⚠️ 没有找到测试文章，跳过ChestnutCMS测试")
            return True
        
        test_article = articles[0]
        print(f"📰 测试文章: [{test_article['content_id']}] {test_article.get('title', '无标题')}")
        
        # 处理文章
        result = cms_service.process_article(test_article, "add")
        
        if result["success"]:
            print("✅ ChestnutCMS文章处理成功!")
            print(f"  - 文章ID: {result['content_id']}")
            print(f"  - 标题: {result['title']}")
            print(f"  - 块数: {result['chunks']}")
        else:
            print(f"❌ ChestnutCMS文章处理失败: {result['message']}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ ChestnutCMS测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔧 开始清理和测试修复...")
    
    # 1. 清理数据库
    if not clean_database():
        print("❌ 数据库清理失败，退出测试")
        return
    
    # 2. 测试基础文档处理
    if not test_document_processing():
        print("❌ 基础文档处理测试失败")
        return
    
    # 3. 测试ChestnutCMS集成
    if not test_chestnut_cms_integration():
        print("❌ ChestnutCMS集成测试失败")
        return
    
    print("\n🎉 所有测试通过！修复成功！")

if __name__ == "__main__":
    main()
