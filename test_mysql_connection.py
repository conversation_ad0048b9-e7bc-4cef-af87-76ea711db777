"""
MySQL 连接测试脚本
快速测试 MySQL 数据库连接
"""
import sys
import os

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from backend.app.services.mysql_service import MySQLService
import json
import logging

def main():
    """主测试函数"""
    # 配置日志
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("🚀 MySQL 连接测试工具")
    print("=" * 60)
    print("测试目标: localhost:3306/chestnut_cms")
    print("=" * 60)
    
    try:
        # 创建 MySQL 服务实例
        mysql_service = MySQLService()
        
        # 执行连接测试
        print("\n🔄 正在测试连接...")
        result = mysql_service.test_connection()
        
        # 输出详细结果
        print("\n📋 详细测试结果:")
        print(json.dumps(result, indent=2, ensure_ascii=False))
        
        if result["success"]:
            print("\n✅ 连接测试成功！")
            
            conn_info = result["connection_info"]
            print(f"\n📊 数据库信息总览:")
            print(f"  🖥️  服务器地址: {conn_info['host']}:{conn_info['port']}")
            print(f"  🗄️  数据库名称: {conn_info['current_database']}")
            print(f"  👤 用户名: {conn_info['user']}")
            print(f"  📌 MySQL版本: {conn_info['mysql_version']}")
            print(f"  🔗 连接ID: {conn_info['connection_id']}")
            print(f"  📊 表数量: {conn_info['tables_count']}")
            
            if conn_info['tables']:
                print(f"\n📄 数据库表列表 ({len(conn_info['tables'])} 个表):")
                for i, table in enumerate(conn_info['tables'], 1):
                    print(f"  {i:2d}. {table}")
                
                # 如果有表，尝试获取第一个表的详细信息
                if conn_info['tables']:
                    first_table = conn_info['tables'][0]
                    print(f"\n🔍 查看表 '{first_table}' 的详细信息:")
                    try:
                        table_info = mysql_service.get_table_info(first_table)
                        print(f"  📊 记录数量: {table_info['record_count']}")
                        print(f"  📋 字段信息:")
                        for col in table_info['columns']:
                            print(f"    - {col['Field']}: {col['Type']} {'(NOT NULL)' if col['Null'] == 'NO' else '(可为空)'}")
                    except Exception as e:
                        print(f"  ⚠️  获取表信息失败: {e}")
            else:
                print(f"\n📄 数据库为空，没有找到任何表")
                
        else:
            print("\n❌ 连接测试失败！")
            print(f"\n💥 错误详情:")
            print(f"  错误信息: {result['message']}")
            if 'error_code' in result and result['error_code']:
                print(f"  错误代码: {result['error_code']}")
            if 'error_details' in result:
                print(f"  详细错误: {result['error_details']}")
            
            print(f"\n🔧 连接配置:")
            if 'connection_config' in result:
                config = result['connection_config']
                print(f"  主机: {config['host']}")
                print(f"  端口: {config['port']}")
                print(f"  数据库: {config['database']}")
                print(f"  用户: {config['user']}")
            
            print(f"\n💡 常见解决方案:")
            print(f"  1. 检查 MySQL 服务是否启动")
            print(f"  2. 确认数据库 'chestnut_cms' 是否存在")
            print(f"  3. 验证用户名密码是否正确")
            print(f"  4. 检查网络连接和防火墙设置")
            print(f"  5. 确认 MySQL 允许从当前IP连接")
    
    except ImportError as e:
        print(f"\n❌ 导入错误: {e}")
        print(f"\n💡 解决方案:")
        print(f"  请安装 MySQL 依赖: pip install pymysql")
    
    except Exception as e:
        print(f"\n❌ 未知错误: {e}")
        print(f"  类型: {type(e).__name__}")
    
    print(f"\n" + "=" * 60)
    print(f"测试完成")
    print(f"=" * 60)


if __name__ == "__main__":
    main()