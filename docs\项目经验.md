# 项目经验总结

## 后端服务管理

关闭后端要优雅，ctrl+c 慢慢等
netstat -ano | findstr :8000
在 windows 本地用 cmd 执行 python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
别用 powershell，容易出现进程残留

## LlamaIndex + ChromaDB 集成经验

### 文档处理最佳实践 (2025-07-05)

**问题描述**：

- 文档上传失败：`Error in compaction: Failed to apply logs to the metadata segment`
- 查询失败：`TextNode.text` 字段为 None 导致 Pydantic 验证错误

**根本原因**：
直接使用 `index.insert(doc)` 插入 Document 对象，没有正确转换为 TextNode

**正确的文档处理流程**：

```python
# ❌ 错误方式 - 直接插入 Document
for doc in documents:
    self.index.insert(doc)

# ✅ 正确方式 - 先转换为 Node 再插入
node_parser = Settings.node_parser
nodes = node_parser.get_nodes_from_documents(documents)
self.index.insert_nodes(nodes)
```

**关键要点**：

1. **Document → NodeParser → TextNode → Index** 是标准流程
2. 使用 `Settings.node_parser` 获取全局配置的分块器
3. 使用 `insert_nodes()` 而不是 `insert()` 来添加节点
4. 确保 SentenceSplitter 正确配置：
   ```python
   Settings.node_parser = SentenceSplitter(
       chunk_size=512,
       chunk_overlap=50
   )
   ```

### 元数据存储最佳实践

**问题**：是否应该在 `_node_content` 中直接添加 `title`、`file_url` 等字段？

**答案**：❌ 不推荐，应该使用 `metadata` 字段

**正确做法**：

```python
# ✅ 推荐：使用 metadata
metadata = {
    "title": "文章标题",
    "file_url": "https://...",
    "content_id": "123456",
    "source": "chestnut_cms",
    "publish_date": "2025-07-01 11:06:39"
}
doc.metadata.update(metadata)
```

**原因**：

1. **架构设计**：ChromaDB + LlamaIndex 采用分层存储
   - `text` 字段：存储在 `embedding_fulltext_search_content` 表
   - `embedding` 字段：存储在 `embeddings` 表
   - `metadata` 字段：存储所有附加信息
2. **兼容性**：LlamaIndex 的序列化机制专门为 metadata 设计
3. **查询支持**：过滤和检索功能针对 metadata 优化
4. **升级安全**：未来版本更新不会破坏 metadata 中的数据

### ChromaDB 存储架构理解

**重要认知**：`_node_content` 中 `text` 字段为空是**正常现象**，不是 bug

**分层存储设计**：
| 数据类型 | 存储位置 | 说明 |
|---------|----------|------|
| 文本内容 | `embedding_fulltext_search_content` 表 | 用于 BM25 全文检索 |
| 向量数据 | `embeddings` 表 + 外部文件 | 用于语义相似度检索 |
| 元数据 | `embedding_metadata` 表 | LlamaIndex 节点信息 |

**设计优势**：

1. 避免数据重复 - 文本内容只存储一份
2. 优化检索性能 - 向量检索和全文检索分别优化
3. 节省存储空间 - 减少冗余数据
4. 支持并行检索 - BM25 和向量检索可并行执行

### 数据库损坏恢复

**问题**：ChromaDB 元数据段损坏
**解决方案**：

```bash
# Windows PowerShell
Remove-Item -Recurse -Force storage -ErrorAction SilentlyContinue
mkdir storage
```

**预防措施**：

1. 定期备份 storage 目录
2. 优雅关闭服务（Ctrl+C 等待完成）
3. 避免强制终止进程

### 测试和验证

**完整测试流程**：

1. 清理数据库（如果损坏）
2. 测试基础文档处理
3. 测试 ChestnutCMS 集成
4. 验证查询功能和元数据

**测试脚本**：`scripts/clean_and_test_fix.py`

### 故障排除指南

**常见错误及解决方案**：

1. **`'TextNode' object has no attribute 'get_doc_id'`**

   - 原因：使用 `insert()` 插入 Node 对象
   - 解决：使用 `insert_nodes()` 方法

2. **`TextNode.text` 字段为 None**

   - 原因：没有正确使用 NodeParser 转换 Document
   - 解决：先用 `get_nodes_from_documents()` 转换

3. **`Error in compaction: Failed to apply logs to the metadata segment`**

   - 原因：ChromaDB 数据库损坏
   - 解决：删除 storage 目录重新创建

4. **查询返回空结果**
   - 检查：文档是否正确上传
   - 检查：嵌入模型配置是否正确
   - 检查：查询引擎是否重新创建

**调试技巧**：

```python
# 检查文档数量
print(f"文档数量: {rag_service.collection.count()}")

# 检查文档内容
result = rag_service.collection.get(include=["documents", "metadatas"])
print(f"第一个文档: {result['documents'][0][:100]}...")

# 检查元数据
print(f"元数据: {result['metadatas'][0]}")
```

### 性能优化建议

1. **分块大小调优**：

   - 中文文档：chunk_size=512 较为合适
   - 英文文档：可以适当增大到 1024
   - overlap=50 保证上下文连续性

2. **嵌入模型选择**：

   - `text-embedding-3-small`：1536 维，性价比高
   - 确保 ChromaDB 集合维度匹配

3. **查询优化**：
   - 使用合适的 `similarity_top_k` 值（默认 5）
   - 考虑实现混合检索（BM25 + 向量）

### 经验教训

1. **遵循官方文档**：LlamaIndex 的文档处理流程有其设计原因
2. **理解底层架构**：ChromaDB 的分层存储不是 bug，是特性
3. **正确使用 API**：`insert_nodes()` vs `insert()` 的区别很重要
4. **元数据管理**：使用 metadata 字段而不是自定义字段
5. **测试驱动**：编写完整的测试脚本验证修复效果
6. **渐进式开发**：先实现基础功能，再添加复杂特性
7. **错误处理**：充分的异常处理和日志记录很重要

### 相关文档

- [LlamaIndex 官方文档](https://docs.llamaindex.ai/)
- [ChromaDB 文档](https://docs.trychroma.com/)
- 项目内部文档：
  - `docs/chromadb-storage-analysis.md` - ChromaDB 存储架构分析
  - `docs/chromadb-storage-summary.md` - 存储问题总结
  - `test_chestnut_cms_integration.py` - 集成测试脚本
  - `scripts/clean_and_test_fix.py` - 修复验证脚本
