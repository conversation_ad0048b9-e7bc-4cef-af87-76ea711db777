# CLAUDE.md

本文件为 Claude Code (claude.ai/code) 在本代码库中工作时提供指导。

## 项目概览

这是一个基于 FastAPI 和 LlamaIndex 构建的 ChestnutCMS RAG 集成系统。系统从 ChestnutCMS 数据库自动同步文章内容，构建知识库，为用户提供基于网站内容的智能问答服务。应用结合了 BM25 关键词搜索和向量相似度搜索进行文档检索，并在回答中显示原文链接。

## 架构设计

### 后端 (FastAPI + LlamaIndex)
- **入口点**: `backend/app/main.py` - 主 FastAPI 应用，包含静态文件服务
- **核心服务**: `backend/app/services/rag_service.py` - 使用 LlamaIndex 和 ChromaDB 的 RAG 功能
- **数据同步**: `backend/app/services/chestnut_cms_service.py` - ChestnutCMS 数据同步服务（业务逻辑层）
- **MySQL 服务**: `backend/app/services/mysql_service.py` - MySQL 数据库连接和查询（数据访问层）
- **配置管理**: `backend/config/settings.py` - 支持环境变量的 Pydantic 配置
- **API 路由**: `backend/app/api/v1/` - 模块化 API 端点（查询、文档、健康检查、同步）

#### 服务架构层次
```
ChestnutCMSService (业务逻辑层)
    ↓ 依赖
MySQLService (数据访问层) + RAGService (文档处理层)
    ↓ 连接
ChestnutCMS MySQL数据库 + ChromaDB向量存储
```

### 前端 (原生 JS + TailwindCSS)
- **模板**: `frontend/templates/index.html` - 主聊天界面
- **文档管理**: `frontend/templates/documents.html` - 文档管理界面，包含 ChestnutCMS 同步功能
- **JavaScript**: `frontend/static/js/app.js` - 聊天应用逻辑，使用 localStorage 存储历史
- **文档管理 JS**: `frontend/static/js/documents.js` - 文档管理和同步进度显示逻辑
- **样式**: `frontend/static/css/style.css` - 基于 TailwindCSS 的响应式设计

### 存储与数据
- **文档目录**: `data/` - 存储从 ChestnutCMS 同步的文章文件 ({content_id}.txt)
- **向量存储**: `storage/` - 基于 SQLite 的 ChromaDB 向量存储
- **ChestnutCMS 数据库**: MySQL 数据库，包含 cms_content、cms_catalog、cms_article_detail 表
- **配置文件**: `.env` - API 密钥、数据库连接和设置的环境变量

## 常用开发命令

### 启动应用

```bash
# 开发模式（自动重载）
cd backend
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload

# 或者：从项目根目录运行
python -m uvicorn backend.app.main:app --host 0.0.0.0 --port 8000 --reload
```

### 安装依赖

```bash
# 安装 Python 依赖
pip install -r requirements.txt

# 创建虚拟环境（如需要）
python -m venv venv
# Windows: venv\Scripts\activate
# Linux/Mac: source venv/bin/activate
```

### 数据库和文档管理

```bash
# 检查数据库状态
python scripts/check_database.py

# 诊断 HNSW 错误（常见的向量搜索问题）
python scripts/diagnose_hnsw_error.py

# 检查嵌入维度
python scripts/check_embedding_dimensions.py

# 重建数据库（修复大多数问题）
python scripts/rebuild_database.py

# 测试 MySQL 连接
python test_mysql_connection.py

# 测试 ChestnutCMS 数据同步
python test_chestnut_cms_sync.py
```

### 测试和验证

```bash
# 测试文档管理功能
python scripts/test_document_management.py

# 测试 API 密钥配置
python test_api_key.py

# 测试 LlamaIndex 集成
python test_llamaindex.py
```

## 关键配置

### 环境变量 (.env)
运行所需的环境变量：
- `OPENAI_API_KEY` - 你的 OpenAI API 密钥
- `OPENAI_BASE_URL` - API 基础 URL（支持代理）
- `OPENAI_MODEL` - LLM 模型（默认：gpt-4o-mini）
- `EMBEDDING_MODEL` - 嵌入模型（默认：text-embedding-3-small）

### ChestnutCMS 数据库配置
- `MYSQL_HOST` - MySQL 服务器地址
- `MYSQL_PORT` - MySQL 端口（默认：3306）
- `MYSQL_USER` - MySQL 用户名
- `MYSQL_PASSWORD` - MySQL 密码
- `MYSQL_DATABASE` - 数据库名（chestnut_cms）
- `CHESTNUT_CMS_BASE_URL` - 网站基础 URL（https://www.gzmdrw.cn）

### 重要路径
- `DATA_DIR=./data` - 文档源目录
- `STORAGE_DIR=./storage` - ChromaDB 存储位置
- `CHROMA_PERSIST_DIRECTORY=./storage` - 向量数据库持久化位置

## ChestnutCMS 数据同步

### 同步机制
系统从 ChestnutCMS 数据库自动同步文章内容：
1. 从 cms_content 表获取文章基本信息（只处理 link_flag 为 NULL 的文章）
2. 从 cms_article_detail 表获取 HTML 内容
3. 从 cms_catalog 表获取分类路径信息
4. 清理 HTML 标签，保留文本内容
5. 构建原文链接 URL
6. 创建 {content_id}.txt 文件并向量化存储

### 数据同步操作
- **新增**：ChestnutCMS 中存在但 RAG 系统中不存在的文章
- **删除**：RAG 系统中存在但 ChestnutCMS 中不存在的文章
- **更新**：基于 publish_date 判断文章是否需要更新
- **保持**：两个系统中数据一致的文章

### 文件替换机制
应用实现基于文件名的文档替换：
1. 处理文档时，同名文件会被完全替换
2. 旧文件的所有块都会从 ChromaDB 中删除
3. 新文件会重新处理和索引
4. 这确保了干净的更新，无重复内容

### API 端点
- `GET /` - 主聊天界面
- `GET /documents` - 文档管理界面
- `POST /api/v1/query` - 提交聊天查询（返回带元数据的回答）
- `POST /api/v1/documents/load` - 从 data 目录重新加载文档
- `POST /api/v1/sync/chestnut-cms` - 同步 ChestnutCMS 文章数据
- `GET /api/v1/sync/status` - 获取同步进度状态
- `GET /api/v1/status` - 系统状态和健康检查

## 故障排除

### HNSW Segment Reader 错误
这是最常见的问题，通常表示向量索引损坏：
```bash
python scripts/diagnose_hnsw_error.py
# 如果问题持续：
python scripts/rebuild_database.py
```

### 维度不匹配错误
检查嵌入模型配置：
```bash
python scripts/check_embedding_dimensions.py
```

### 性能问题
推荐在以下情况使用 `rebuild_database.py` 脚本：
- 每月维护
- 系统升级后
- 查询变慢时
- 存储优化

### 数据库验证
SQLite 存储结构在直接查询时显示 `text` 字段为空，`embedding` 字段为 null - 这是正常的 ChromaDB 行为，文本存储在 FTS 表中，向量存储在专门的嵌入表中。

## 开发提示

### 调试
- 后端日志提供详细的操作信息
- 前端控制台显示 API 交互和聊天状态
- 所有脚本都输出全面的诊断信息

### 文件结构导航
- 配置逻辑: `backend/config/settings.py:Settings`
- RAG 实现: `backend/app/rag_service.py:RAGService`
- ChestnutCMS 同步: `backend/app/services/chestnut_cms_service.py:ChestnutCMSService`
- MySQL 连接: `backend/app/services/mysql_service.py:MySQLService`
- API 路由: `backend/app/api/v1/api.py`
- 前端聊天逻辑: `frontend/static/js/app.js:ChatApp`
- 文档管理逻辑: `frontend/static/js/documents.js`

### ChromaDB 管理
- 集合名称可通过 `COLLECTION_NAME` 配置（默认："documents"）
- 支持基于 content_id 的文档管理，支持 ChestnutCMS 文章的增删改查
- 存储扩展元数据：content_id、title、file_url、publish_date、source
- 数据库重建时自动创建备份
- 基于 SQLite 的存储，简单且可移植

## 元数据结构

### ChromaDB 元数据字段
```python
metadata = {
    # 原有字段
    "file_path": "data/{content_id}.txt",
    "file_name": "{content_id}.txt", 
    "filename": "{content_id}.txt",
    
    # ChestnutCMS 扩展字段
    "content_id": "12345",              # 文章 ID
    "title": "文章标题",                # 文章标题
    "file_url": "https://www.gzmdrw.cn/path/12345.shtml",  # 原文链接
    "publish_date": "2024-01-01",       # 发布日期
    "source": "chestnut_cms"            # 数据来源
}
```

### 查询响应格式
RAG 查询现在返回扩展的元数据信息，前端会显示文章标题并支持点击跳转到原文：
```json
{
  "answer": "AI生成的回答",
  "sources": [
    {
      "content": "相关文档片段",
      "score": 0.85,
      "metadata": {
        "content_id": "12345",
        "title": "文章标题",
        "file_url": "https://www.gzmdrw.cn/path/12345.shtml",
        "publish_date": "2024-01-01"
      }
    }
  ]
}
```

## 新增服务架构详解

### ChestnutCMSService 核心功能

#### 数据同步流程
1. **数据获取**: 通过 MySQLService 从 ChestnutCMS 获取文章数据
2. **内容处理**: 使用 BeautifulSoup 清理 HTML，提取纯文本
3. **URL 构建**: 根据分类路径和文章ID构建原文链接
4. **同步策略**: 比较 CMS 和 RAG 系统，确定增删改查操作
5. **元数据更新**: 为每个文档块更新扩展元数据字段

#### HTML 内容清理策略
- 移除 script、style、img、video、audio 等标签
- 保留表格中的文本内容，移除表格结构
- 清理 HTML 实体字符
- 合并多余空行和空格
- 保持段落结构

#### 同步操作类型
- **新增**: CMS中存在但RAG系统中不存在的文章
- **更新**: 基于 publish_date 判断需要更新的文章
- **删除**: RAG系统中存在但CMS中不存在的文章
- **保持**: 两个系统中数据一致的文章

### MySQLService 扩展功能

#### ChestnutCMS 专用方法
- `get_chestnut_cms_articles()`: 获取所有有效文章（link_flag为NULL）
- `get_chestnut_cms_article_by_id()`: 根据content_id获取单篇文章
- `get_chestnut_cms_articles_summary()`: 获取文章统计信息

#### 数据查询策略
```sql
-- 核心查询SQL，联合三个表获取完整文章信息
SELECT 
    c.content_id, c.catalog_id, c.title, c.publish_date, 
    c.link_flag, c.redirect_url, cat.path, ad.content_html
FROM cms_content c
LEFT JOIN cms_catalog cat ON c.catalog_id = cat.catalog_id  
LEFT JOIN cms_article_detail ad ON c.content_id = ad.content_id
WHERE c.link_flag IS NULL
ORDER BY c.publish_date DESC
```

### 依赖关系图
```
ChestnutCMSService
├── MySQLService (数据访问)
│   ├── 获取CMS文章列表
│   ├── 获取单篇文章详情
│   └── 获取文章统计信息
├── RAGService (文档处理)
│   ├── 文档上传和删除
│   ├── 获取现有文档列表
│   └── 元数据更新
└── BeautifulSoup (HTML处理)
    ├── HTML标签清理
    ├── 多媒体内容移除
    └── 文本格式化
```

### 配置依赖更新
```txt
# 新增依赖
beautifulsoup4==4.12.3  # HTML内容解析和清理
```

### 文件结构导航更新
- ChestnutCMS同步逻辑: `backend/app/services/chestnut_cms_service.py:ChestnutCMSService`
- MySQL数据访问: `backend/app/services/mysql_service.py:MySQLService`
- HTML内容处理: `backend/app/services/chestnut_cms_service.py:clean_html_content()`
- URL构建逻辑: `backend/app/services/chestnut_cms_service.py:build_article_url()`
- 元数据管理: `backend/app/services/chestnut_cms_service.py:_update_document_metadata()`