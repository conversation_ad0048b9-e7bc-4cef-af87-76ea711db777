#!/usr/bin/env python3
"""
ChestnutCMS RAG 集成系统功能测试脚本
测试各个核心组件的基础功能
"""
import sys
import os
import logging
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.config.settings import settings
from backend.app.services.mysql_service import MySQLService
from backend.app.services.chestnut_cms_service import ChestnutCMSService

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)


def test_mysql_connection():
    """测试MySQL连接"""
    print("\n" + "="*50)
    print("🔍 测试MySQL连接")
    print("="*50)
    
    try:
        mysql_service = MySQLService()
        result = mysql_service.test_connection()
        
        if result["success"]:
            print("✅ MySQL连接测试成功!")
            conn_info = result["connection_info"]
            print(f"  - 服务器: {conn_info['host']}:{conn_info['port']}")
            print(f"  - 数据库: {conn_info['current_database']}")
            print(f"  - MySQL版本: {conn_info['mysql_version']}")
            print(f"  - 表数量: {conn_info['tables_count']}")
            return True
        else:
            print("❌ MySQL连接测试失败!")
            print(f"  错误: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ MySQL连接测试异常: {e}")
        return False


def test_chestnut_cms_articles():
    """测试ChestnutCMS文章获取"""
    print("\n" + "="*50)
    print("📄 测试ChestnutCMS文章获取")
    print("="*50)
    
    try:
        mysql_service = MySQLService()
        
        # 获取文章统计
        summary = mysql_service.get_chestnut_cms_articles_summary()
        print(f"📊 文章统计:")
        print(f"  - 总文章数: {summary['total_articles']}")
        print(f"  - 有内容文章数: {summary['articles_with_content']}")
        print(f"  - 最新日期: {summary['latest_date']}")
        print(f"  - 最老日期: {summary['oldest_date']}")
        
        # 获取前5篇文章
        articles = mysql_service.get_chestnut_cms_articles()
        print(f"\n📝 前5篇文章:")
        for i, article in enumerate(articles[:5], 1):
            title = article['title'] or f"文章{article['content_id']}"
            print(f"  {i}. [{article['content_id']}] {title}")
            print(f"     发布: {article['publish_date']}, 路径: {article['path']}")
        
        return len(articles) > 0
        
    except Exception as e:
        print(f"❌ 文章获取测试失败: {e}")
        return False


def test_html_cleaning():
    """测试HTML清理功能"""
    print("\n" + "="*50)
    print("🧹 测试HTML清理功能")
    print("="*50)
    
    try:
        cms_service = ChestnutCMSService()
        mysql_service = MySQLService()
        
        # 获取真实文章的HTML内容进行测试
        articles = mysql_service.get_chestnut_cms_articles()
        
        if not articles:
            print("❌ 没有找到文章数据")
            return False
        
        # 找一篇有内容的文章
        test_article = None
        for article in articles[:10]:  # 检查前10篇
            if article.get('content_html'):
                test_article = article
                break
        
        if not test_article:
            print("❌ 没有找到包含HTML内容的文章")
            return False
        
        content_id = test_article['content_id']
        title = test_article.get('title', f'文章{content_id}')
        html_content = test_article['content_html']
        
        print(f"🔧 测试文章: [{content_id}] {title}")
        print(f"原始HTML长度: {len(html_content)} 字符")
        print("\n原始HTML内容（前500字符）:")
        print("-" * 50)
        print(html_content[:500] + ("..." if len(html_content) > 500 else ""))
        print("-" * 50)
        
        # 执行HTML清理
        cleaned_text = cms_service.clean_html_content(html_content)
        
        print(f"\n清理后文本长度: {len(cleaned_text)} 字符")
        print("\n清理后内容:")
        print("=" * 50)
        print(cleaned_text)
        print("=" * 50)
        
        # 简单检查清理效果
        has_html_tags = '<' in cleaned_text and '>' in cleaned_text
        has_meaningful_text = len(cleaned_text.strip()) > 0
        
        print(f"\n✅ 移除HTML标签: {'成功' if not has_html_tags else '失败'}")
        print(f"✅ 保留有意义文本: {'成功' if has_meaningful_text else '失败'}")
        
        return not has_html_tags and has_meaningful_text
        
    except Exception as e:
        print(f"❌ HTML清理测试失败: {e}")
        return False


def test_url_building():
    """测试URL构建功能"""
    print("\n" + "="*50)
    print("🔗 测试URL构建功能")
    print("="*50)
    
    try:
        cms_service = ChestnutCMSService()
        mysql_service = MySQLService()
        
        # 获取前5篇真实文章来测试URL构建
        articles = mysql_service.get_chestnut_cms_articles()
        
        if not articles:
            print("❌ 没有找到文章数据")
            return False
        
        print("🔧 使用真实文章数据测试URL构建:")
        
        for i, article in enumerate(articles[:5], 1):
            content_id = str(article['content_id'])
            path = article.get('path', '')
            title = article.get('title', f'文章{content_id}')
            
            # 使用服务构建URL
            built_url = cms_service.build_article_url(content_id, path)
            
            print(f"📄 第{i}篇文章:")
            print(f"  标题: {title}")
            print(f"  文章ID: {content_id}")
            print(f"  原始路径: '{path}'")
            print(f"  构建的URL: {built_url}")
            print()
        
        return True
        
    except Exception as e:
        print(f"❌ URL构建测试失败: {e}")
        return False


def test_chestnut_cms_service():
    """测试ChestnutCMS服务连接"""
    print("\n" + "="*50)
    print("🔄 测试ChestnutCMS服务连接")
    print("="*50)
    
    try:
        cms_service = ChestnutCMSService()
        result = cms_service.test_connection()
        
        if result["success"]:
            print("✅ ChestnutCMS服务连接成功!")
            print(f"  - MySQL信息: {result['mysql_info']['current_database']}")
            summary = result['articles_summary']
            print(f"  - 文章统计: 总数={summary['total_articles']}, 有内容={summary['articles_with_content']}")
            return True
        else:
            print("❌ ChestnutCMS服务连接失败!")
            print(f"  错误: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ ChestnutCMS服务测试异常: {e}")
        return False


def test_article_upload():
    """测试单篇文章上传功能"""
    print("\n" + "="*50)
    print("📤 测试单篇文章上传功能")
    print("="*50)
    
    try:
        cms_service = ChestnutCMSService()
        mysql_service = MySQLService()
        
        # 获取一篇真实文章来测试上传
        articles = mysql_service.get_chestnut_cms_articles()
        
        if not articles:
            print("❌ 没有找到文章数据")
            return False
        
        # 找一篇有内容的文章
        test_article = None
        for article in articles[:10]:  # 检查前10篇
            if article.get('content_html') and len(article['content_html'].strip()) > 100:
                test_article = article
                break
        
        if not test_article:
            print("❌ 没有找到包含足够HTML内容的文章")
            return False
        
        content_id = test_article['content_id']
        title = test_article.get('title', f'文章{content_id}')
        
        print(f"🔧 测试上传文章: [{content_id}] {title}")
        print(f"原始HTML长度: {len(test_article['content_html'])} 字符")
        
        # 执行文章处理（add操作）
        result = cms_service.process_article(test_article, "add")
        
        if result["success"]:
            print("✅ 文章上传成功!")
            print(f"  - 文章ID: {result['content_id']}")
            print(f"  - 标题: {result['title']}")
            print(f"  - 文档块数: {result['chunks']}")
            print(f"  - 文章URL: {result['file_url']}")
            
            # 验证元数据是否正确存储
            print("\n🔍 验证元数据存储...")
            rag_service = cms_service.rag_service
            
            # 查看文档列表
            docs_result = rag_service.get_documents_list()
            if docs_result["success"]:
                # 查找我们刚上传的文档
                uploaded_doc = None
                for doc in docs_result["documents"]:
                    if doc.get("content_id") == str(content_id):
                        uploaded_doc = doc
                        break
                
                if uploaded_doc:
                    print("✅ 文档元数据验证:")
                    print(f"  - filename: {uploaded_doc.get('filename')}")
                    print(f"  - file_path: {uploaded_doc.get('file_path')}")
                    print(f"  - file_size: {uploaded_doc.get('file_size')}")
                    print(f"  - content_id: {uploaded_doc.get('content_id')}")
                    print(f"  - title: {uploaded_doc.get('title')}")
                    print(f"  - file_url: {uploaded_doc.get('file_url')}")
                    print(f"  - publish_date: {uploaded_doc.get('publish_date')}")
                    print(f"  - source: {uploaded_doc.get('source')}")
                    print(f"  - chunks_count: {uploaded_doc.get('chunks_count')}")
                    
                    # 检查必要字段是否存在
                    required_fields = ['filename', 'file_path', 'content_id', 'title', 'file_url', 'source']
                    missing_fields = [field for field in required_fields if not uploaded_doc.get(field)]
                    
                    if missing_fields:
                        print(f"⚠️ 缺少必要字段: {missing_fields}")
                        return False
                    else:
                        print("✅ 所有必要元数据字段都存在")
                        return True
                else:
                    print("❌ 未找到上传的文档")
                    return False
            else:
                print(f"❌ 获取文档列表失败: {docs_result['message']}")
                return False
        else:
            print("❌ 文章上传失败!")
            print(f"  错误: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 文章上传测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_query_with_metadata():
    """测试查询功能是否返回正确的元数据"""
    print("\n" + "="*50)
    print("🔍 测试查询功能和元数据返回")
    print("="*50)
    
    try:
        cms_service = ChestnutCMSService()
        rag_service = cms_service.rag_service
        
        # 执行一个简单查询
        query = "介绍一下"
        result = rag_service.query(query, max_results=3)
        
        if result["success"]:
            print(f"✅ 查询成功: {query}")
            print(f"找到 {len(result['sources'])} 个相关文档")
            
            for i, source in enumerate(result['sources'], 1):
                print(f"\n📄 第{i}个匹配文档:")
                print(f"  - 内容片段: {source['content'][:100]}...")
                print(f"  - 相似度分数: {source['score']:.4f}")
                
                metadata = source.get('metadata', {})
                print(f"  - 元数据:")
                print(f"    * filename: {metadata.get('filename')}")
                print(f"    * content_id: {metadata.get('content_id')}")
                print(f"    * title: {metadata.get('title')}")
                print(f"    * file_url: {metadata.get('file_url')}")
                print(f"    * publish_date: {metadata.get('publish_date')}")
                print(f"    * source: {metadata.get('source')}")
            
            # 检查是否有ChestnutCMS来源的文档
            cms_sources = [s for s in result['sources'] if s.get('metadata', {}).get('source') == 'chestnut_cms']
            if cms_sources:
                print(f"\n✅ 找到 {len(cms_sources)} 个ChestnutCMS来源的文档")
                return True
            else:
                print("\n⚠️ 没有找到ChestnutCMS来源的文档，可能需要先上传文章")
                return True  # 查询功能正常，只是没有相关文档
        else:
            print(f"❌ 查询失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 查询测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_setup():
    """测试环境配置"""
    print("\n" + "="*50)
    print("⚙️ 测试环境配置")
    print("="*50)
    
    issues = []
    
    # 检查关键配置
    if not settings.openai_api_key:
        issues.append("OPENAI_API_KEY 未设置")
    
    if not settings.mysql_host:
        issues.append("MYSQL_HOST 未设置")
        
    if not settings.mysql_database:
        issues.append("MYSQL_DATABASE 未设置")
    
    # 检查目录
    if not settings.data_dir.exists():
        issues.append(f"数据目录不存在: {settings.data_dir}")
    
    if not settings.storage_dir.exists():
        issues.append(f"存储目录不存在: {settings.storage_dir}")
    
    if issues:
        print("❌ 环境配置问题:")
        for issue in issues:
            print(f"  - {issue}")
        return False
    else:
        print("✅ 环境配置正常!")
        print(f"  - OpenAI模型: {settings.openai_model}")
        print(f"  - 嵌入模型: {settings.embedding_model}")
        print(f"  - 数据目录: {settings.data_dir}")
        print(f"  - 存储目录: {settings.storage_dir}")
        print(f"  - ChestnutCMS URL: {settings.chestnut_cms_base_url}")
        return True


def main():
    """主测试函数"""
    print("🚀 开始ChestnutCMS RAG集成系统功能测试")
    print("这个测试脚本将验证各个核心组件的基础功能")
    
    tests = [
        ("环境配置", test_environment_setup),
        ("MySQL连接", test_mysql_connection),
        ("ChestnutCMS文章获取", test_chestnut_cms_articles),
        ("HTML清理功能", test_html_cleaning),
        ("URL构建功能", test_url_building),
        ("ChestnutCMS服务", test_chestnut_cms_service),
        ("单篇文章上传", test_article_upload),
        ("查询功能和元数据", test_query_with_metadata),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} 测试异常: {e}")
            results.append((test_name, False))
    
    # 输出测试总结
    print("\n" + "="*50)
    print("📋 测试结果总结")
    print("="*50)
    
    passed = 0
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"  {test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\n🎯 总体结果: {passed}/{total} 测试通过")
    
    if passed == total:
        print("🎉 所有测试通过! 系统准备就绪。")
        return 0
    else:
        print("⚠️ 部分测试失败，请检查相关配置和服务。")
        return 1


if __name__ == "__main__":
    sys.exit(main())