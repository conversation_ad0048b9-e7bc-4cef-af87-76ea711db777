#!/usr/bin/env python3
"""
测试当前检索系统是否正常工作
"""
import sys
import os
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from backend.app.services.rag_service import RAGService
import logging

# 设置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

def test_retrieval_system():
    """测试检索系统"""
    try:
        print("🔍 初始化RAG服务...")
        rag_service = RAGService()
        
        print("📊 获取系统状态...")
        status = rag_service.get_status()
        print(f"  - 文档数量: {status.get('documents_count', 0)}")
        print(f"  - 存储大小: {status.get('storage_size', 'N/A')}")
        
        if status.get('documents_count', 0) == 0:
            print("❌ 没有文档，无法测试检索")
            return False
        
        print("\n🔍 测试向量检索...")
        test_queries = [
            "RAG是什么？",
            "大数据",
            "贵阳",
            "毕业生"
        ]
        
        for query in test_queries:
            print(f"\n查询: {query}")
            try:
                response = rag_service.query(query)
                print(f"  ✓ 响应长度: {len(response.get('answer', ''))}")
                print(f"  ✓ 响应预览: {response.get('answer', '')[:100]}...")
                
                # 检查是否有检索到的文档
                sources = response.get('sources', [])
                print(f"  ✓ 检索到的文档数: {len(sources)}")
                for i, source in enumerate(sources[:2]):  # 只显示前2个
                    print(f"    - 文档{i+1}: {source.get('filename', 'N/A')}")
                    
            except Exception as e:
                print(f"  ❌ 查询失败: {e}")
                return False
        
        print("\n✅ 检索系统测试完成，系统工作正常")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

def test_chromadb_data_access():
    """测试ChromaDB数据访问"""
    try:
        print("\n🔍 测试ChromaDB数据访问...")
        rag_service = RAGService()
        
        # 获取一些文档数据
        result = rag_service.collection.get(
            limit=3,
            include=["documents", "metadatas"]
        )
        
        print(f"  ✓ 获取到 {len(result['ids'])} 条记录")
        
        for i, doc_id in enumerate(result['ids']):
            document = result['documents'][i] if result['documents'] else None
            metadata = result['metadatas'][i] if result['metadatas'] else {}
            
            print(f"\n  记录 {i+1} (ID: {doc_id[:8]}...):")
            print(f"    - 文档内容长度: {len(document) if document else 0}")
            print(f"    - 文档内容预览: {document[:100] if document else 'None'}...")
            print(f"    - 文件名: {metadata.get('filename', 'N/A')}")
        
        return True
        
    except Exception as e:
        print(f"❌ ChromaDB数据访问测试失败: {e}")
        return False

if __name__ == "__main__":
    print("🚀 开始测试检索系统")
    print("=" * 50)
    
    # 测试检索功能
    retrieval_ok = test_retrieval_system()
    
    # 测试数据访问
    data_access_ok = test_chromadb_data_access()
    
    print("\n" + "=" * 50)
    if retrieval_ok and data_access_ok:
        print("✅ 所有测试通过，系统工作正常")
        print("💡 _node_content中text为空是正常现象，文本内容存储在ChromaDB的documents字段中")
    else:
        print("❌ 测试失败，系统可能存在问题")
